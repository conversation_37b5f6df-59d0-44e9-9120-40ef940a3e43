import React, { useState } from 'react'
import '../App.css'

const choices = [
    { id: 'rock', name: 'Rock', emoji: '🪨', beats: 'scissors' },
    { id: 'paper', name: 'Paper', emoji: '📄', beats: 'rock' },
    { id: 'scissors', name: 'Scissors', emoji: '✂️', beats: 'paper' }
]

const game_playe = () => {
   const [player1, setPlayer1] = useState({ name: '', choice: null, score: 0 })
   const [player2, setPlayer2] = useState({ name: '', choice: null, score: 0 })
   const [currentPlayer, setCurrentPlayer] = useState(1)
   const [gameResult, setGameResult] = useState(null)
   const [showResult, setShowResult] = useState(false)
   const [isAnimating, setIsAnimating] = useState(false)
   const [currentRound, setCurrentRound] = useState(1)
   const [gameFinished, setGameFinished] = useState(false)
   const [tournamentWinner, setTournamentWinner] = useState(null)

   const resetRound = () => {
    setPlayer1({ name: player1.name, choice: null, score: player1.score })
    setPlayer2({ name: player2.name, choice: null, score: player2.score })
    setCurrentPlayer(1)
    setGameResult(null)
    setShowResult(false)
    setIsAnimating(false)
  }

  const resetAll = () => {
    setPlayer1({ name: '', choice: null, score: 0 })
    setPlayer2({ name: '', choice: null, score: 0 })
    setCurrentPlayer(1)
    setGameResult(null)
    setShowResult(false)
    setIsAnimating(false)
    setCurrentRound(1)
    setGameFinished(false)
    setTournamentWinner(null)
  }

  const determineWinner = (player1Choice, player2Choice) => {
      if(player1Choice.id === player2Choice.id) return 'tie'
      return player1Choice.beats === player2Choice.id ? 'player1' : 'player2'
  }

  const checkTournamentEnd = (p1Score, p2Score, round) => {
    // Check if someone has won 6 games (majority of 10)
    if (p1Score >= 6) {
      setTournamentWinner('player1')
      setGameFinished(true)
      return true
    }
    if (p2Score >= 6) {
      setTournamentWinner('player2')
      setGameFinished(true)
      return true
    }
    // Check if all 10 rounds are completed
    if (round >= 10) {
      if (p1Score > p2Score) {
        setTournamentWinner('player1')
      } else if (p2Score > p1Score) {
        setTournamentWinner('player2')
      } else {
        setTournamentWinner('tie')
      }
      setGameFinished(true)
      return true
    }
    return false
  }
  const handleChoice = (choice) => {
    if (currentPlayer === 1) {
      setPlayer1({...player1, choice})
      setCurrentPlayer(2)
    } else {
      setPlayer2({...player2, choice})
      setIsAnimating(true)

      setTimeout(() => {
        const winner = determineWinner(player1.choice, choice)
        setGameResult(winner)

        let newP1Score = player1.score
        let newP2Score = player2.score

        if (winner === 'player1') {
          newP1Score = player1.score + 1
          setPlayer1(prev => ({...prev, score: newP1Score}))
        } else if (winner === 'player2') {
          newP2Score = player2.score + 1
          setPlayer2(prev => ({...prev, score: newP2Score}))
        }

        setShowResult(true)
        setIsAnimating(false)

        // Check if tournament should end
        const tournamentEnded = checkTournamentEnd(newP1Score, newP2Score, currentRound)

        if (!tournamentEnded) {
          // Move to next round
          setCurrentRound(prev => prev + 1)
        }
      }, 1500)
    }
  }
  if(!player1.name || !player2.name) {
    return (
        <div className='setup-container'>
            <h2 className="setup-title">Enter Player Names</h2>
            {!player1.name && (
                <div className='input-group'>
                    <label>Player 1 Name:</label>
                    <input
                    type="text"
                    placeholder="Enter your name"
                    onKeyDown={(e) => {
                        if (e.key === 'Enter' && e.target.value.trim()) {
                        setPlayer1({ ...player1, name: e.target.value.trim() })
                        }
                    }}
                    />
                </div>
            )}
            {player1.name && !player2.name && (
                <div className='input-group'>
                    <label>Player 2 Name:</label>
                    <input
                    type="text"
                    placeholder="Enter your name"
                    onKeyDown={(e) => {
                        if (e.key === 'Enter' && e.target.value.trim()) {
                        setPlayer2({ ...player2, name: e.target.value.trim() })
                        }
                    }}
                    />
                </div>
            )}
            <button className="back-btn" onClick={resetAll}>
                ← Back to Menu
            </button>
        </div>
    )
  }
  return (
    <div className="app">
      <div className="stars"></div>
      <div className='game-container'>
        <div className='tournament-header'>
          <h2 className='tournament-title'>🏆 TOURNAMENT MODE - BEST OF 10 ROUNDS 🏆</h2>
          <div className='round-info'>
            <span className='round-counter'>Round {currentRound} of 10</span>
            <div className='progress-bar'>
              <div
                className='progress-fill'
                style={{width: `${(currentRound - 1) * 10}%`}}
              ></div>
            </div>
          </div>
        </div>

        <div className='game-header'>
            <div className='player-info'>
                <h3>{player1.name}</h3>
                <div className='score'>{player1.score}</div>
                <div className='score-label'>Wins</div>
            </div>
            <div className='vs-divider'>VS</div>
            <div className='player-info'>
                <h3>{player2.name}</h3>
                <div className='score'>{player2.score}</div>
                <div className='score-label'>Wins</div>
            </div>
        </div>
            {currentPlayer === 2 && !player2.choice &&(
                <div className="turn-indicator">
                    <h2>{player2.name}'s Turn</h2>
                    <p>{player1.name} has made their choice!</p>
                </div>
            )}
            {(currentPlayer === 1 || (currentPlayer === 2 && player2.choice)) && !showResult &&(
                <div className='choice-section'>
                    <div className="choices">
                        {choices.map((choice) => (
                            <button
                                key={choice.id}
                                className="choice-btn"
                                onClick={() => handleChoice(choice)}
                                disabled={isAnimating}
                            >
                                <span className="choice-emoji">{choice.emoji}</span>
                                <span className="choice-name">{choice.name}</span>
                            </button>
                        ))}  
                    </div>
                </div>
            )}
            {isAnimating && (
                <div className="animation-section">
                    <h2>Rock... Paper... Scissors!</h2>
                    <div className="countdown-animation">
                        <div className="choice-display">
                            <div className="player-choice">
                                <h3>{player1.name}</h3>
                                <div className="choice-emoji animate">{player1.choice?.emoji}</div>
                            </div>
                            <div className="vs">VS</div>
                            <div className="player-choice">
                  <h3>{player2.name}</h3>
                  <div className="choice-emoji animate">{player2.choice?.emoji || '❓'}</div>
                </div>
              </div>
            </div>
          </div>
        )}
   {showResult && (
          <div className="result-section">
            <div className="choices-reveal">
              <div className="player-choice">
                <h3>{player1.name}</h3>
                <div className="choice-emoji">{player1.choice?.emoji}</div>
                <p>{player1.choice?.name}</p>
              </div>
              <div className="vs">VS</div>
              <div className="player-choice">
                <h3>{player2.name}</h3>
                <div className="choice-emoji">{player2.choice?.emoji}</div>
                <p>{player2.choice?.name}</p>
              </div>
            </div>

            <div className="result-announcement">
              {gameResult === 'tie' && <h2 className="tie">It's a Tie! 🤝</h2>}
              {gameResult === 'player1' && <h2 className="winner">{player1.name} Wins! 🎉</h2>}
              {gameResult === 'player2' && <h2 className="winner">{player2.name} Wins! 🎉</h2>}
            </div>

            <div className="game-controls">
              {!gameFinished ? (
                <button className="play-again-btn" onClick={resetRound}>
                  Next Round
                </button>
              ) : (
                <button className="play-again-btn" onClick={resetAll}>
                  New Tournament
                </button>
              )}
              <button className="menu-btn-small" onClick={resetAll}>
                Main Menu
              </button>
            </div>
          </div>
        )}

        {gameFinished && (
          <div className="tournament-end">
            <div className="tournament-result">
              <h1 className="tournament-title">🏆 TOURNAMENT COMPLETE! 🏆</h1>

              <div className="final-scores">
                <div className="final-score-card">
                  <h3>{player1.name}</h3>
                  <div className="final-score">{player1.score}</div>
                  <p>Rounds Won</p>
                </div>
                <div className="vs-final">VS</div>
                <div className="final-score-card">
                  <h3>{player2.name}</h3>
                  <div className="final-score">{player2.score}</div>
                  <p>Rounds Won</p>
                </div>
              </div>

              <div className="tournament-winner">
                {tournamentWinner === 'tie' && (
                  <h2 className="tie-result">🤝 IT'S A TIE! 🤝</h2>
                )}
                {tournamentWinner === 'player1' && (
                  <h2 className="winner-result">🎉 {player1.name} WINS THE TOURNAMENT! 🎉</h2>
                )}
                {tournamentWinner === 'player2' && (
                  <h2 className="winner-result">🎉 {player2.name} WINS THE TOURNAMENT! 🎉</h2>
                )}
              </div>

              <div className="tournament-controls">
                <button className="new-tournament-btn" onClick={resetAll}>
                  🆕 New Tournament
                </button>
              </div>
            </div>
          </div>
        )}

        </div>
      </div>
    </div>
  )
}

export default game_playe
