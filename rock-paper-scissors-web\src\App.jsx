import React, { useState } from 'react'
import './App.css'
import { NavLink } from 'react-router-dom'
const App = () => {
  const [gameStarted, setGameStarted] = useState(false)

  if (!gameStarted) {
    return (
      <div className="app">
        <div className="stars"></div>
        <div className="welcome-container">
          {/* Header Section */}
          <div className="header-section">
            <div className="game-logo">
              <div className="logo-icon">🎮</div>
              <h1 className="main-title">
                <span className="title-gradient">ROCK PAPER SCISSORS</span>
              </h1>
              <div className="title-underline"></div>
            </div>
          </div>

          {/* Team Section */}
          <div className="team-section">
            <div className="team-card">
              <div className="team-header">
                <h2 className="team-title">
                  <span className="team-icon">👥</span>
                  GROUP 5
                </h2>
                <p className="team-subtitle">The Ultimate Gaming Squad</p>
              </div>
              </div>
            </div>
          <div className="action-section">
            <button
              className="start-game-btn"
              onClick={() => setGameStarted(true)}
            >
              <span className="btn-icon">🎯</span>
              <span className="btn-text">START GAME</span>
              <span className="btn-arrow">→</span>
            </button>

            <div className="game-preview">
              <div className="preview-choices">
                <div className="choice-preview">🪨</div>
                <div className="vs-text">VS</div>
                <div className="choice-preview">📄</div>
                <div className="vs-text">VS</div>
                <div className="choice-preview">✂️</div>
              </div>
              <p className="preview-text">Choose your weapon wisely!</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Game Mode Selection Screen
  return (
    <div className="app">
      <div className="stars"></div>
      <div className="game-mode-container">

        <div className="mode-options">
          <div className="mode-card pvp-mode">
            <div className="mode-icon-large">👥</div>
            <h3 className="mode-name">Player vs Player</h3>
            <p className="mode-description">Challenge a friend in epic battles</p>
            <ul className="mode-features">
              <li>✨ Turn-based gameplay</li>
              <li>🏆 Score tracking</li>
              <li>🎭 Privacy mode</li>
            </ul>
            <NavLink to="/game" className="mode-btn pvp-btn">
              Choose PvP
            </NavLink>
          </div>
        </div>

        <button
          className="back-to-welcome-btn"
          onClick={() => setGameStarted(false)}
        >
          ← Back to Welcome
        </button>
      </div>
    </div>
  )
}

export default App
