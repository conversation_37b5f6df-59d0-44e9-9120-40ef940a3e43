import os
import time

def clear_screen():
    """Clear the console screen"""
    os.system('cls' if os.name == 'nt' else 'clear')

def get_player_choice(player_name):
    """Get a valid choice from the player"""
    choices = ['rock', 'paper', 'scissors']
    
    while True:
        print(f"\n{player_name}'s turn:")
        print("Choose: rock, paper, or scissors")
        choice = input("Enter your choice: ").lower().strip()
        
        if choice in choices:
            return choice
        else:
            print("Invalid choice! Please choose rock, paper, or scissors.")

def determine_winner(p1_choice, p2_choice):
    """Determine the winner based on game rules"""
    if p1_choice == p2_choice:
        return "tie"
    
    winning_combinations = {
        'rock': 'scissors',
        'paper': 'rock',
        'scissors': 'paper'
    }
    
    if winning_combinations[p1_choice] == p2_choice:
        return "player1"
    else:
        return "player2"

def display_choices(p1_name, p1_choice, p2_name, p2_choice):
    """Display both players' choices"""
    print(f"\n{p1_name} chose: {p1_choice.upper()}")
    print(f"{p2_name} chose: {p2_choice.upper()}")
    print("-" * 30)

def display_result(winner, p1_name, p2_name):
    """Display the result of the round"""
    if winner == "tie":
        print("It's a TIE!")
    elif winner == "player1":
        print(f"{p1_name} WINS this round!")
    else:
        print(f"{p2_name} WINS this round!")

def display_score(p1_name, p1_score, p2_name, p2_score):
    """Display current score"""
    print(f"\nCurrent Score:")
    print(f"{p1_name}: {p1_score}")
    print(f"{p2_name}: {p2_score}")
    print("-" * 30)

def play_game():
    """Main game function"""
    print("🎮 ROCK, PAPER, SCISSORS - 2 PLAYER GAME 🎮")
    print("=" * 50)
    
    # Get player names
    p1_name = input("Enter Player 1's name: ").strip() or "Player 1"
    p2_name = input("Enter Player 2's name: ").strip() or "Player 2"
    
    # Initialize scores
    p1_score = 0
    p2_score = 0
    
    print(f"\nWelcome {p1_name} and {p2_name}!")
    print("Game Rules:")
    print("- Rock beats Scissors")
    print("- Paper beats Rock")
    print("- Scissors beats Paper")
    print("\nLet's begin!")
    
    while True:
        input("\nPress Enter to start the round...")
        clear_screen()
        
        # Player 1's turn
        p1_choice = get_player_choice(p1_name)
        
        # Clear screen so Player 2 can't see Player 1's choice
        clear_screen()
        print("Player 1 has made their choice!")
        
        # Player 2's turn
        p2_choice = get_player_choice(p2_name)
        
        # Show results
        clear_screen()
        display_choices(p1_name, p1_choice, p2_name, p2_choice)
        
        # Determine winner
        winner = determine_winner(p1_choice, p2_choice)
        display_result(winner, p1_name, p2_name)
        
        # Update scores
        if winner == "player1":
            p1_score += 1
        elif winner == "player2":
            p2_score += 1
        
        # Display current score
        display_score(p1_name, p1_score, p2_name, p2_score)
        
        # Ask if players want to continue
        play_again = input("\nDo you want to play another round? (y/n): ").lower().strip()
        if play_again not in ['y', 'yes']:
            break
    
    # Final results
    print("\n" + "=" * 50)
    print("FINAL RESULTS:")
    print(f"{p1_name}: {p1_score} wins")
    print(f"{p2_name}: {p2_score} wins")
    
    if p1_score > p2_score:
        print(f"\n🎉 {p1_name} is the OVERALL WINNER! 🎉")
    elif p2_score > p1_score:
        print(f"\n🎉 {p2_name} is the OVERALL WINNER! 🎉")
    else:
        print(f"\n🤝 It's a TIE GAME! Great match! 🤝")
    
    print("\nThanks for playing!")

if __name__ == "__main__":
    play_game()