import { useState } from 'react'
import './App.css'

const choices = [
  { id: 'rock', name: 'Rock', emoji: '🪨', beats: 'scissors' },
  { id: 'paper', name: 'Paper', emoji: '📄', beats: 'rock' },
  { id: 'scissors', name: 'Scissors', emoji: '✂️', beats: 'paper' }
]

function App() {
  const [gameMode, setGameMode] = useState('menu') // 'menu', 'pvp', 'pvc'
  const [player1, setPlayer1] = useState({ name: '', choice: null, score: 0 })
  const [player2, setPlayer2] = useState({ name: '', choice: null, score: 0 })
  const [currentPlayer, setCurrentPlayer] = useState(1)
  const [gameResult, setGameResult] = useState(null)
  const [showResult, setShowResult] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)

  const resetGame = () => {
    setPlayer1({ name: player1.name, choice: null, score: player1.score })
    setPlayer2({ name: player2.name, choice: null, score: player2.score })
    setCurrentPlayer(1)
    setGameResult(null)
    setShowResult(false)
    setIsAnimating(false)
  }

  const resetAll = () => {
    setPlayer1({ name: '', choice: null, score: 0 })
    setPlayer2({ name: '', choice: null, score: 0 })
    setCurrentPlayer(1)
    setGameResult(null)
    setShowResult(false)
    setIsAnimating(false)
    setGameMode('menu')
  }

  const getComputerChoice = () => {
    const randomIndex = Math.floor(Math.random() * choices.length)
    return choices[randomIndex]
  }


  const determineWinner = (p1Choice, p2Choice) => {
    if (p1Choice.id === p2Choice.id) return 'tie'
    return p1Choice.beats === p2Choice.id ? 'player1' : 'player2'
  }

  const handleChoice = (choice) => {
    if (gameMode === 'pvc') {
      // Player vs Computer
      const computerChoice = getComputerChoice()
      setPlayer1({ ...player1, choice })
      setPlayer2({ ...player2, choice: computerChoice })

      setIsAnimating(true)
      setTimeout(() => {
        const winner = determineWinner(choice, computerChoice)
        setGameResult(winner)

        if (winner === 'player1') {
          setPlayer1(prev => ({ ...prev, score: prev.score + 1 }))
        } else if (winner === 'player2') {
          setPlayer2(prev => ({ ...prev, score: prev.score + 1 }))
        }

        setShowResult(true)
        setIsAnimating(false)
      }, 1500)
    } else {
      // Player vs Player
      if (currentPlayer === 1) {
        setPlayer1({ ...player1, choice })
        setCurrentPlayer(2)
        //SWITCH TO PLAYER 2
      } else {
        setPlayer2({ ...player2, choice })

        setIsAnimating(true)
        setTimeout(() => {
          const winner = determineWinner(player1.choice, choice)
          setGameResult(winner)

          if (winner === 'player1') {
            setPlayer1(prev => ({ ...prev, score: prev.score + 1 }))
          } else if (winner === 'player2') {
            setPlayer2(prev => ({ ...prev, score: prev.score + 1 }))
          }

          setShowResult(true)
          setIsAnimating(false)
        }, 1500)
      }
    }
  }

  const startGame = (mode) => {
    setGameMode(mode)
    if (mode === 'pvc') {
      setPlayer2({ name: 'Computer', choice: null, score: 0 })
    }
  }

  if (gameMode === 'menu') {
    return (
      <div className="app">
        <div className="menu-container">
          <h1 className="game-title">
            <span className="title-emoji">🎮</span>
            Rock Paper Scissors
            <span className="title-emoji">🎮</span>
          </h1>
          <div className="menu-buttons">
            <button
              className="menu-btn pvp-btn"
              onClick={() => startGame('pvp')}
            >
              <span className="btn-emoji">👥</span>
              Player vs Player
            </button>
            <button
              className="menu-btn pvc-btn"
              onClick={() => startGame('pvc')}
            >
              <span className="btn-emoji">🤖</span>
              Player vs Computer
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (!player1.name || (!player2.name && gameMode === 'pvp')) {
    return (
      <div className="app">
        <div className="setup-container">
          <h2 className="setup-title">Enter Player Names</h2>
          {!player1.name && (
            <div className="input-group">
              <label>Player 1 Name:</label>
              <input
                type="text"
                placeholder="Enter your name"
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && e.target.value.trim()) {
                    setPlayer1({ ...player1, name: e.target.value.trim() })
                  }
                }}
              />
            </div>
          )}
          {gameMode === 'pvp' && player1.name && !player2.name && (
            <div className="input-group">
              <label>Player 2 Name:</label>
              <input
                type="text"
                placeholder="Enter your name"
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && e.target.value.trim()) {
                    setPlayer2({ ...player2, name: e.target.value.trim() })
                  }
                }}
              />
            </div>
          )}
          <button className="back-btn" onClick={resetAll}>
            ← Back to Menu
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="app">
      <div className="game-container">
        <div className="game-header">
          <div className="player-info">
            <h3>{player1.name}</h3>
            <div className="score">{player1.score}</div>
          </div>
          <div className="vs-divider">VS</div>
          <div className="player-info">
            <h3>{player2.name}</h3>
            <div className="score">{player2.score}</div>
          </div>
        </div>

        {gameMode === 'pvp' && currentPlayer === 2 && !player2.choice && (
          <div className="turn-indicator">
            <h2>{player2.name}'s Turn</h2>
            <p>{player1.name} has made their choice!</p>
          </div>
        )}

        {(gameMode === 'pvc' || currentPlayer === 1 || (currentPlayer === 2 && gameMode === 'pvp')) && !showResult && (
          <div className="choice-section">
            <h2>
              {gameMode === 'pvc' ? 'Choose your weapon!' :
               currentPlayer === 1 ? `${player1.name}, choose your weapon!` :
               `${player2.name}, choose your weapon!`}
            </h2>
            <div className="choices">
              {choices.map((choice) => (
                <button
                  key={choice.id}
                  className="choice-btn"
                  onClick={() => handleChoice(choice)}
                  disabled={isAnimating}
                >
                  <span className="choice-emoji">{choice.emoji}</span>
                  <span className="choice-name">{choice.name}</span>
                </button>
              ))}
            </div>
          </div>
        )}

        {isAnimating && (
          <div className="animation-section">
            <h2>Rock... Paper... Scissors!</h2>
            <div className="countdown-animation">
              <div className="choice-display">
                <div className="player-choice">
                  <h3>{player1.name}</h3>
                  <div className="choice-emoji animate">{player1.choice?.emoji}</div>
                </div>
                <div className="vs">VS</div>
                <div className="player-choice">
                  <h3>{player2.name}</h3>
                  <div className="choice-emoji animate">{player2.choice?.emoji || '❓'}</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {showResult && (
          <div className="result-section">
            <div className="choices-reveal">
              <div className="player-choice">
                <h3>{player1.name}</h3>
                <div className="choice-emoji">{player1.choice?.emoji}</div>
                <p>{player1.choice?.name}</p>
              </div>
              <div className="vs">VS</div>
              <div className="player-choice">
                <h3>{player2.name}</h3>
                <div className="choice-emoji">{player2.choice?.emoji}</div>
                <p>{player2.choice?.name}</p>
              </div>
            </div>

            <div className="result-announcement">
              {gameResult === 'tie' && <h2 className="tie">It's a Tie! 🤝</h2>}
              {gameResult === 'player1' && <h2 className="winner">{player1.name} Wins! 🎉</h2>}
              {gameResult === 'player2' && <h2 className="winner">{player2.name} Wins! 🎉</h2>}
            </div>

            <div className="game-controls">
              <button className="play-again-btn" onClick={resetGame}>
                Play Again
              </button>
              <button className="menu-btn-small" onClick={resetAll}>
                Main Menu
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default App
