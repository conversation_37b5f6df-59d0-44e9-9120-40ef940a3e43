* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  padding: 20px;
}

/* Menu Styles */
.menu-container {
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 60px 40px;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  max-width: 500px;
  width: 100%;
}

.game-title {
  font-size: 3rem;
  color: #333;
  margin-bottom: 40px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.title-emoji {
  font-size: 2.5rem;
  margin: 0 15px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.menu-buttons {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.menu-btn {
  padding: 20px 30px;
  font-size: 1.3rem;
  border: none;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.pvp-btn {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
}

.pvc-btn {
  background: linear-gradient(45deg, #4834d4, #686de0);
  color: white;
}

.menu-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.btn-emoji {
  font-size: 1.5rem;
}

/* Setup Styles */
.setup-container {
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  max-width: 400px;
  width: 100%;
}

.setup-title {
  color: #333;
  margin-bottom: 30px;
  font-size: 2rem;
}

.input-group {
  margin-bottom: 25px;
}

.input-group label {
  display: block;
  margin-bottom: 10px;
  color: #555;
  font-weight: bold;
}

.input-group input {
  width: 100%;
  padding: 15px;
  border: 2px solid #ddd;
  border-radius: 10px;
  font-size: 1.1rem;
  transition: border-color 0.3s ease;
}

.input-group input:focus {
  outline: none;
  border-color: #667eea;
}

.back-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: background 0.3s ease;
}

.back-btn:hover {
  background: #5a6268;
}

/* Game Styles */
.game-container {
  background: rgba(255, 255, 255, 0.95);
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  max-width: 800px;
  width: 100%;
  text-align: center;
}

.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(45deg, #f8f9fa, #e9ecef);
  border-radius: 15px;
}

.player-info h3 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.3rem;
}

.score {
  font-size: 2.5rem;
  font-weight: bold;
  color: #667eea;
  background: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.vs-divider {
  font-size: 2rem;
  font-weight: bold;
  color: #666;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.turn-indicator {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(45deg, #ffeaa7, #fdcb6e);
  border-radius: 15px;
}

.turn-indicator h2 {
  color: #333;
  margin-bottom: 10px;
}

.turn-indicator p {
  color: #666;
  font-size: 1.1rem;
}

/* Choice Section */
.choice-section h2 {
  color: #333;
  margin-bottom: 30px;
  font-size: 1.8rem;
}

.choices {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.choice-btn {
  background: white;
  border: 3px solid #ddd;
  border-radius: 20px;
  padding: 30px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.choice-btn:hover {
  transform: translateY(-5px);
  border-color: #667eea;
  box-shadow: 0 15px 30px rgba(102, 126, 234, 0.3);
}

.choice-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.choice-emoji {
  font-size: 4rem;
  transition: transform 0.3s ease;
}

.choice-btn:hover .choice-emoji {
  transform: scale(1.1);
}

.choice-name {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
}

/* Animation Section */
.animation-section {
  margin: 40px 0;
}

.animation-section h2 {
  color: #333;
  margin-bottom: 30px;
  font-size: 2rem;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.countdown-animation {
  display: flex;
  justify-content: center;
  align-items: center;
}

.choice-display {
  display: flex;
  align-items: center;
  gap: 40px;
}

.player-choice {
  text-align: center;
}

.player-choice h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.choice-emoji.animate {
  font-size: 5rem;
  animation: shake 0.5s infinite;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.vs {
  font-size: 1.5rem;
  font-weight: bold;
  color: #666;
}

/* Result Section */
.result-section {
  margin-top: 30px;
}

.choices-reveal {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  margin-bottom: 30px;
  padding: 30px;
  background: linear-gradient(45deg, #f8f9fa, #e9ecef);
  border-radius: 15px;
}

.choices-reveal .player-choice {
  text-align: center;
}

.choices-reveal .choice-emoji {
  font-size: 4rem;
  margin: 15px 0;
}

.choices-reveal p {
  color: #666;
  font-size: 1.1rem;
  font-weight: bold;
}

.result-announcement {
  margin: 30px 0;
}

.result-announcement h2 {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.result-announcement .winner {
  color: #28a745;
  animation: celebrate 0.6s ease-in-out;
}

.result-announcement .tie {
  color: #ffc107;
}

@keyframes celebrate {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.game-controls {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}

.play-again-btn {
  background: linear-gradient(45deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 10px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.play-again-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(40, 167, 69, 0.3);
}

.menu-btn-small {
  background: #6c757d;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 10px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.menu-btn-small:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 10px;
  }

  .game-title {
    font-size: 2rem;
  }

  .menu-container {
    padding: 40px 20px;
  }

  .game-header {
    flex-direction: column;
    gap: 20px;
  }

  .choices {
    gap: 15px;
  }

  .choice-btn {
    min-width: 120px;
    padding: 20px 15px;
  }

  .choice-emoji {
    font-size: 3rem;
  }

  .choices-reveal {
    flex-direction: column;
    gap: 20px;
  }

  .choice-display {
    flex-direction: column;
    gap: 20px;
  }

  .game-controls {
    flex-direction: column;
    align-items: center;
  }
}
