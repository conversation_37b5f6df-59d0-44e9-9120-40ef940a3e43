import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import Game from "./pages/game_playe.jsx"
import App from './App.jsx'
import './App.css'
import { BrowserRouter, Route, Routes } from 'react-router-dom'
createRoot(document.getElementById('root')).render(
  <StrictMode>
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<App />} />
        <Route path='game' element={<Game />} />
      </Routes>
    </BrowserRouter>
  </StrictMode>,
)
