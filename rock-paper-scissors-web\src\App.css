/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

.app {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}

/* Animated background stars */
.stars {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-image:
    radial-gradient(2px 2px at 20px 30px, #eee, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #fff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
    radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: sparkle 3s linear infinite;
}

@keyframes sparkle {
  from { transform: translateY(0px); }
  to { transform: translateY(-100px); }
}

/* Welcome Screen Styles */
.welcome-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 50px 40px;
  max-width: 800px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.header-section {
  margin-bottom: 50px;
}

.game-logo {
  margin-bottom: 30px;
}

.logo-icon {
  font-size: 5rem;
  margin-bottom: 20px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-15px); }
  60% { transform: translateY(-8px); }
}

.main-title {
  font-size: 3.5rem;
  margin-bottom: 20px;
  font-weight: 900;
  letter-spacing: 2px;
}

.title-gradient {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.title-underline {
  width: 200px;
  height: 4px;
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
  margin: 0 auto;
  border-radius: 2px;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; transform: scaleX(1); }
  50% { opacity: 1; transform: scaleX(1.1); }
}

/* Team Section */
.team-section {
  margin-bottom: 50px;
}

.team-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 40px 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.team-header {
  margin-bottom: 30px;
}

.team-title {
  color: white;
  font-size: 2.2rem;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  font-weight: 700;
}

.team-icon {
  font-size: 2.5rem;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.team-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.2rem;
  font-style: italic;
}

.members-title {
  color: white;
  font-size: 1.5rem;
  margin-bottom: 25px;
  font-weight: 600;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}
.member-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 25px 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.member-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.member-avatar {
  font-size: 3rem;
  margin-bottom: 15px;
  animation: float 3s ease-in-out infinite;
}

.member-card:nth-child(2) .member-avatar {
  animation-delay: 0.5s;
}

.member-card:nth-child(3) .member-avatar {
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.member-name {
  color: white;
  font-size: 1.3rem;
  margin-bottom: 8px;
  font-weight: 600;
}

.member-role {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
  font-style: italic;
}

/* Action Section */
.action-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.start-game-btn {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border: none;
  border-radius: 50px;
  padding: 20px 40px;
  color: white;
  font-size: 1.4rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.start-game-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #ff5252, #26d0ce);
}

.btn-icon {
  font-size: 1.8rem;
  animation: pulse 2s infinite;
}

.btn-text {
  font-size: 1.4rem;
}

.btn-arrow {
  font-size: 1.6rem;
  transition: transform 0.3s ease;
}

.start-game-btn:hover .btn-arrow {
  transform: translateX(8px);
}

.game-preview {
  text-align: center;
}

.preview-choices {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 15px;
}

.choice-preview {
  font-size: 2.5rem;
  animation: bounce 2s infinite;
  transition: transform 0.3s ease;
}

.choice-preview:nth-child(1) { animation-delay: 0s; }
.choice-preview:nth-child(3) { animation-delay: 0.3s; }
.choice-preview:nth-child(5) { animation-delay: 0.6s; }

.choice-preview:hover {
  transform: scale(1.3) rotate(10deg);
}

.vs-text {
  color: rgba(255, 255, 255, 0.8);
  font-weight: bold;
  font-size: 1.2rem;
}

.preview-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  font-style: italic;
}

/* Game Mode Selection Screen */
.game-mode-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 50px 40px;
  max-width: 900px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.mode-header {
  margin-bottom: 50px;
}

.mode-title {
  color: white;
  font-size: 2.5rem;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  font-weight: 700;
}

.mode-icon {
  font-size: 3rem;
  animation: swing 2s ease-in-out infinite;
}

@keyframes swing {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(10deg); }
  75% { transform: rotate(-10deg); }
}

.mode-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.3rem;
}

.mode-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}
.mode-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 40px 30px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.mode-card:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.mode-icon-large {
  font-size: 4rem;
  margin-bottom: 20px;
  animation: bounce 2s infinite;
}

.pvp-mode .mode-icon-large {
  animation-delay: 0s;
}

.pvc-mode .mode-icon-large {
  animation-delay: 0.5s;
}

.mode-name {
  color: white;
  font-size: 1.8rem;
  margin-bottom: 15px;
  font-weight: 600;
}

.mode-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin-bottom: 20px;
  line-height: 1.5;
}

.mode-features {
  list-style: none;
  margin-bottom: 25px;
}

.mode-features li {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  margin-bottom: 8px;
  padding-left: 10px;
}

.mode-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  border-radius: 25px;
  padding: 15px 30px;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.mode-btn:hover {
  background: linear-gradient(45deg, #5a6fd8, #6a4190);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.back-to-welcome-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 15px 30px;
  color: white;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-to-welcome-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .welcome-container {
    padding: 30px 20px;
    max-width: 95%;
  }

  .main-title {
    font-size: 2.5rem;
  }

  .team-title {
    font-size: 1.8rem;
  }

  .members-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .start-game-btn {
    padding: 15px 30px;
    font-size: 1.2rem;
  }

  .game-mode-container {
    padding: 30px 20px;
    max-width: 95%;
  }

  .mode-options {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .mode-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2rem;
  }

  .logo-icon {
    font-size: 4rem;
  }

  .team-title {
    font-size: 1.5rem;
    flex-direction: column;
    gap: 10px;
  }

  .member-card {
    padding: 20px 15px;
  }

  .start-game-btn {
    padding: 12px 25px;
    font-size: 1.1rem;
  }

  .preview-choices {
    gap: 10px;
  }

  .choice-preview {
    font-size: 2rem;
  }
}

/* Tournament Game Styles */
.game-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 40px;
  max-width: 900px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.tournament-header {
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tournament-title {
  color: white;
  font-size: 1.8rem;
  margin-bottom: 15px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.round-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.round-counter {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.2rem;
  font-weight: 600;
}

.progress-bar {
  width: 300px;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
  border-radius: 4px;
  transition: width 0.5s ease;
}

.game-header {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  gap: 30px;
  margin-bottom: 30px;
  padding: 25px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
}

.player-info {
  text-align: center;
}

.player-info h3 {
  color: white;
  font-size: 1.5rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.score {
  font-size: 3rem;
  font-weight: 900;
  color: #4ecdc4;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: 5px;
}

.score-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
  font-weight: 500;
}

.vs-divider {
  color: white;
  font-size: 2rem;
  font-weight: 900;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.turn-indicator {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  margin: 20px 0;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.turn-indicator h2 {
  color: white;
  font-size: 1.8rem;
  margin-bottom: 10px;
}

.turn-indicator p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
}

.choice-section {
  margin: 30px 0;
}

.choices {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.choice-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 30px 20px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.choice-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.choice-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.choice-emoji {
  font-size: 3rem;
  display: block;
  margin-bottom: 10px;
  animation: bounce 2s infinite;
}

.choice-name {
  font-size: 1.2rem;
  font-weight: 600;
}

.animation-section {
  margin: 40px 0;
  padding: 30px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
}

.animation-section h2 {
  color: white;
  font-size: 2rem;
  margin-bottom: 30px;
  animation: pulse 1s infinite;
}

.choice-display {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  gap: 30px;
}

.player-choice {
  text-align: center;
}

.player-choice h3 {
  color: white;
  font-size: 1.3rem;
  margin-bottom: 15px;
}

.choice-emoji.animate {
  font-size: 4rem;
  animation: shake 0.5s infinite;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.vs {
  color: white;
  font-size: 2rem;
  font-weight: 900;
}

.result-section {
  margin: 30px 0;
  padding: 30px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.choices-reveal {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  gap: 30px;
  margin-bottom: 30px;
}

.choices-reveal .player-choice {
  text-align: center;
}

.choices-reveal .choice-emoji {
  font-size: 4rem;
  margin: 15px 0;
}

.choices-reveal p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
}

.result-announcement {
  margin: 30px 0;
}

.result-announcement h2 {
  font-size: 2.5rem;
  margin-bottom: 15px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.winner {
  color: #4ecdc4;
  animation: celebration 1s ease-in-out;
}

.tie {
  color: #feca57;
}

@keyframes celebration {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.game-controls {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.play-again-btn, .menu-btn-small {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  border-radius: 25px;
  padding: 15px 30px;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.play-again-btn:hover, .menu-btn-small:hover {
  background: linear-gradient(45deg, #5a6fd8, #6a4190);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}
/* Tournament End Screen */
.tournament-end {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.tournament-result {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 30px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 50px 40px;
  max-width: 600px;
  width: 90%;
  text-align: center;
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.tournament-result .tournament-title {
  font-size: 2.5rem;
  margin-bottom: 30px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease infinite;
}

.final-scores {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  gap: 30px;
  margin: 40px 0;
  padding: 30px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
}

.final-score-card {
  text-align: center;
}

.final-score-card h3 {
  color: white;
  font-size: 1.8rem;
  margin-bottom: 15px;
  font-weight: 600;
}

.final-score {
  font-size: 4rem;
  font-weight: 900;
  color: #4ecdc4;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  margin-bottom: 10px;
  animation: pulse 2s infinite;
}

.final-score-card p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  font-weight: 500;
}

.vs-final {
  color: white;
  font-size: 2.5rem;
  font-weight: 900;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.tournament-winner {
  margin: 40px 0;
}

.winner-result {
  color: #4ecdc4;
  font-size: 2.8rem;
  font-weight: 900;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: celebration 2s ease-in-out infinite;
}

.tie-result {
  color: #feca57;
  font-size: 2.8rem;
  font-weight: 900;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

.tournament-controls {
  margin-top: 40px;
}

.new-tournament-btn {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border: none;
  border-radius: 50px;
  padding: 20px 40px;
  color: white;
  font-size: 1.3rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.new-tournament-btn:hover {
  background: linear-gradient(45deg, #ff5252, #26d0ce);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

/* Setup Screen Styles */
.setup-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 50px 40px;
  max-width: 500px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.setup-title {
  color: white;
  font-size: 2rem;
  margin-bottom: 30px;
  font-weight: 700;
}

.input-group {
  margin-bottom: 25px;
}

.input-group label {
  display: block;
  color: white;
  font-size: 1.2rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.input-group input {
  width: 100%;
  padding: 15px 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1.1rem;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.input-group input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.input-group input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

.back-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 12px 25px;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 20px;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Mobile Responsive for Tournament */
@media (max-width: 768px) {
  .game-container {
    padding: 20px;
    max-width: 95%;
  }

  .tournament-title {
    font-size: 1.4rem;
  }

  .progress-bar {
    width: 250px;
  }

  .game-header {
    grid-template-columns: 1fr;
    gap: 20px;
    text-align: center;
  }

  .vs-divider {
    order: 2;
  }

  .choices {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .choice-btn {
    padding: 20px 15px;
  }

  .choice-emoji {
    font-size: 2.5rem;
  }

  .tournament-result {
    padding: 30px 20px;
  }

  .final-scores {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .vs-final {
    order: 2;
  }
}