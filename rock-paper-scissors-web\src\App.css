/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

.app {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}

/* Animated background stars */
.stars {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-image:
    radial-gradient(2px 2px at 20px 30px, #eee, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #fff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
    radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: sparkle 3s linear infinite;
}

@keyframes sparkle {
  from { transform: translateY(0px); }
  to { transform: translateY(-100px); }
}

/* Welcome Screen Styles */
.welcome-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 50px 40px;
  max-width: 800px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.header-section {
  margin-bottom: 50px;
}

.game-logo {
  margin-bottom: 30px;
}

.logo-icon {
  font-size: 5rem;
  margin-bottom: 20px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-15px); }
  60% { transform: translateY(-8px); }
}

.main-title {
  font-size: 3.5rem;
  margin-bottom: 20px;
  font-weight: 900;
  letter-spacing: 2px;
}

.title-gradient {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.title-underline {
  width: 200px;
  height: 4px;
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
  margin: 0 auto;
  border-radius: 2px;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; transform: scaleX(1); }
  50% { opacity: 1; transform: scaleX(1.1); }
}

/* Team Section */
.team-section {
  margin-bottom: 50px;
}

.team-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 40px 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.team-header {
  margin-bottom: 30px;
}

.team-title {
  color: white;
  font-size: 2.2rem;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  font-weight: 700;
}

.team-icon {
  font-size: 2.5rem;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.team-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.2rem;
  font-style: italic;
}

.members-title {
  color: white;
  font-size: 1.5rem;
  margin-bottom: 25px;
  font-weight: 600;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}
.member-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 25px 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.member-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.member-avatar {
  font-size: 3rem;
  margin-bottom: 15px;
  animation: float 3s ease-in-out infinite;
}

.member-card:nth-child(2) .member-avatar {
  animation-delay: 0.5s;
}

.member-card:nth-child(3) .member-avatar {
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.member-name {
  color: white;
  font-size: 1.3rem;
  margin-bottom: 8px;
  font-weight: 600;
}

.member-role {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
  font-style: italic;
}

/* Action Section */
.action-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.start-game-btn {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border: none;
  border-radius: 50px;
  padding: 20px 40px;
  color: white;
  font-size: 1.4rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.start-game-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #ff5252, #26d0ce);
}

.btn-icon {
  font-size: 1.8rem;
  animation: pulse 2s infinite;
}

.btn-text {
  font-size: 1.4rem;
}

.btn-arrow {
  font-size: 1.6rem;
  transition: transform 0.3s ease;
}

.start-game-btn:hover .btn-arrow {
  transform: translateX(8px);
}

.game-preview {
  text-align: center;
}

.preview-choices {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 15px;
}

.choice-preview {
  font-size: 2.5rem;
  animation: bounce 2s infinite;
  transition: transform 0.3s ease;
}

.choice-preview:nth-child(1) { animation-delay: 0s; }
.choice-preview:nth-child(3) { animation-delay: 0.3s; }
.choice-preview:nth-child(5) { animation-delay: 0.6s; }

.choice-preview:hover {
  transform: scale(1.3) rotate(10deg);
}

.vs-text {
  color: rgba(255, 255, 255, 0.8);
  font-weight: bold;
  font-size: 1.2rem;
}

.preview-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  font-style: italic;
}

/* Game Mode Selection Screen */
.game-mode-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 50px 40px;
  max-width: 900px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.mode-header {
  margin-bottom: 50px;
}

.mode-title {
  color: white;
  font-size: 2.5rem;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  font-weight: 700;
}

.mode-icon {
  font-size: 3rem;
  animation: swing 2s ease-in-out infinite;
}

@keyframes swing {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(10deg); }
  75% { transform: rotate(-10deg); }
}

.mode-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.3rem;
}

.mode-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}
.mode-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 40px 30px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.mode-card:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.mode-icon-large {
  font-size: 4rem;
  margin-bottom: 20px;
  animation: bounce 2s infinite;
}

.pvp-mode .mode-icon-large {
  animation-delay: 0s;
}

.pvc-mode .mode-icon-large {
  animation-delay: 0.5s;
}

.mode-name {
  color: white;
  font-size: 1.8rem;
  margin-bottom: 15px;
  font-weight: 600;
}

.mode-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin-bottom: 20px;
  line-height: 1.5;
}

.mode-features {
  list-style: none;
  margin-bottom: 25px;
}

.mode-features li {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  margin-bottom: 8px;
  padding-left: 10px;
}

.mode-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  border-radius: 25px;
  padding: 15px 30px;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.mode-btn:hover {
  background: linear-gradient(45deg, #5a6fd8, #6a4190);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.back-to-welcome-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 15px 30px;
  color: white;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-to-welcome-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .welcome-container {
    padding: 30px 20px;
    max-width: 95%;
  }

  .main-title {
    font-size: 2.5rem;
  }

  .team-title {
    font-size: 1.8rem;
  }

  .members-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .start-game-btn {
    padding: 15px 30px;
    font-size: 1.2rem;
  }

  .game-mode-container {
    padding: 30px 20px;
    max-width: 95%;
  }

  .mode-options {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .mode-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2rem;
  }

  .logo-icon {
    font-size: 4rem;
  }

  .team-title {
    font-size: 1.5rem;
    flex-direction: column;
    gap: 10px;
  }

  .member-card {
    padding: 20px 15px;
  }

  .start-game-btn {
    padding: 12px 25px;
    font-size: 1.1rem;
  }

  .preview-choices {
    gap: 10px;
  }

  .choice-preview {
    font-size: 2rem;
  }
}
